import { Router } from 'express';
import * as storeController from '../controllers/storeController';
import * as storeOrderController from '../controllers/storeOrderController';
import { authMiddleware } from '@/middlewares/adminAuth';
import multer from 'multer';
import path from 'path';
import fs from 'fs';

const router = Router();

// Configure multer for store item images
const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    const dir = path.join('uploads', 'store');
    fs.mkdirSync(dir, { recursive: true });
    cb(null, dir);
  },
  filename: (req, file, cb) => {
    const ext = path.extname(file.originalname);
    const name = path.basename(file.originalname, ext).replace(/\s+/g, '-');
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1e9);
    cb(null, `${name}-${uniqueSuffix}${ext}`);
  },
});

const upload = multer({
  storage,
  limits: { fileSize: 5 * 1024 * 1024 }, // 5MB limit
  fileFilter: (req, file, cb) => {
    if (!file.mimetype.startsWith('image/')) {
      return cb(new Error('Only image files are allowed'));
    }
    cb(null, true);
  },
});

router.get('/', storeController.getAllStoreItems);

router.get('/stats', storeController.getStoreStats);

router.get('/:id', storeController.getStoreItemById);

router.post('/', upload.single('image'), storeController.createStoreItem);

router.put('/:id', authMiddleware, upload.single('image'), storeController.updateStoreItem);

router.delete('/:id', authMiddleware, storeController.deleteStoreItem);

// Store order routes - using simple working implementation
router.get('/orders', async (req, res) => {
  try {
    const prisma = require('@/config/prismaClient').default;
    const { status, search, startDate, endDate } = req.query;

    let where: any = {};

    // Apply filters
    if (status) {
      where.status = status;
    }

    if (startDate || endDate) {
      where.createdAt = {};
      if (startDate) {
        where.createdAt.gte = new Date(startDate as string);
      }
      if (endDate) {
        where.createdAt.lte = new Date(endDate as string);
      }
    }

    if (search) {
      const searchTerm = (search as string).toLowerCase();
      where.OR = [
        { id: { contains: searchTerm, mode: 'insensitive' } },
        { studentId: { contains: searchTerm, mode: 'insensitive' } },
        { studentName: { contains: searchTerm, mode: 'insensitive' } },
        { studentEmail: { contains: searchTerm, mode: 'insensitive' } },
        { itemName: { contains: searchTerm, mode: 'insensitive' } }
      ];
    }

    const orders = await prisma.storeOrder.findMany({
      where,
      orderBy: { createdAt: 'desc' }
    });

    console.log(`Successfully retrieved ${orders.length} orders`);
    res.json({
      success: true,
      data: orders,
      message: 'Store orders retrieved successfully'
    });
  } catch (error: any) {
    console.error('Error retrieving store orders:', error);
    res.status(500).json({
      success: false,
      message: error.message || 'Failed to retrieve store orders'
    });
  }
});

router.get('/orders/stats', storeOrderController.getOrderStats);

router.get('/orders/:orderId', storeOrderController.getOrderDetails);

router.put('/orders/:orderId', storeOrderController.updateOrder);

export default router;
