import prisma from '../../../config/prismaClient';

export const getAllStoreOrders = async (filters?: {
  status?: string;
  search?: string;
  startDate?: string;
  endDate?: string;
}) => {
  try {
    console.log('=== STORE ORDERS SERVICE CALLED ===');
    console.log('Filters received:', filters);

    const where: any = {};

    if (filters?.status) {
      where.status = filters.status;
    }

    if (filters?.startDate || filters?.endDate) {
      where.createdAt = {};
      if (filters.startDate) {
        where.createdAt.gte = new Date(filters.startDate);
      }
      if (filters.endDate) {
        where.createdAt.lte = new Date(filters.endDate);
      }
    }

    // Add search filter
    if (filters?.search) {
      const searchTerm = filters.search.toLowerCase();
      where.OR = [
        { id: { contains: searchTerm, mode: 'insensitive' } },
        { studentId: { contains: searchTerm, mode: 'insensitive' } },
        { studentName: { contains: searchTerm, mode: 'insensitive' } },
        { studentEmail: { contains: searchTerm, mode: 'insensitive' } },
        { itemName: { contains: searchTerm, mode: 'insensitive' } }
      ];
    }

    console.log('Where clause:', JSON.stringify(where, null, 2));

    // First try to get orders with item relationship
    let orders;
    try {
      orders = await prisma.storeOrder.findMany({
        where,
        include: {
          item: true
        },
        orderBy: { createdAt: 'desc' }
      });
      console.log(`Successfully fetched ${orders.length} orders with item details`);
    } catch (relationError: any) {
      console.warn('Failed to fetch with item relationship, trying without:', relationError.message);
      // Fallback to orders without relationships
      orders = await prisma.storeOrder.findMany({
        where,
        orderBy: { createdAt: 'desc' }
      });
      console.log(`Successfully fetched ${orders.length} orders without item details`);
    }

    // Log first few orders for debugging
    if (orders.length > 0) {
      console.log('Sample order:', JSON.stringify(orders[0], null, 2));
    }

    return orders;

  } catch (error: any) {
    console.error('=== STORE ORDERS SERVICE ERROR ===');
    console.error('Error message:', error.message);
    console.error('Error code:', error.code);
    console.error('Full error:', error);

    // Throw error instead of returning empty array so we can debug
    throw new Error(`Failed to fetch store orders: ${error.message}`);
  }
};

export const getStoreOrderById = async (orderId: string) => {
  try {
    const order = await prisma.storeOrder.findUnique({
      where: { id: orderId }
    });

    if (!order) {
      throw new Error('Order not found');
    }

    return order;
  } catch (error: any) {
    throw new Error('Failed to fetch order details');
  }
};

export const getStoreOrderStats = async () => {
  try {
    const [
      totalOrders,
      completedOrders,
      pendingOrders,
      cancelledOrders,
      totalRevenue,
      todayOrders,
      thisMonthOrders
    ] = await Promise.all([
      prisma.storeOrder.count(),
      prisma.storeOrder.count({ where: { status: 'COMPLETED' } }),
      prisma.storeOrder.count({ where: { status: 'PENDING' } }),
      prisma.storeOrder.count({ where: { status: 'CANCELLED' } }),
      prisma.storeOrder.aggregate({
        where: { status: 'COMPLETED' },
        _sum: { totalCoins: true }
      }),
      prisma.storeOrder.count({
        where: {
          createdAt: {
            gte: new Date(new Date().setHours(0, 0, 0, 0))
          }
        }
      }),
      prisma.storeOrder.count({
        where: {
          createdAt: {
            gte: new Date(new Date().getFullYear(), new Date().getMonth(), 1)
          }
        }
      })
    ]);

    return {
      totalOrders,
      completedOrders,
      pendingOrders,
      cancelledOrders,
      totalRevenue: totalRevenue._sum.totalCoins || 0,
      todayOrders,
      thisMonthOrders
    };
  } catch (error: any) {
    throw new Error('Failed to fetch order statistics');
  }
};

export const updateOrderStatus = async (orderId: string, status: string) => {
  try {
    const validStatuses = ['PENDING', 'COMPLETED', 'CANCELLED'];
    if (!validStatuses.includes(status)) {
      throw new Error('Invalid order status');
    }

    const order = await prisma.storeOrder.update({
      where: { id: orderId },
      data: { status: status as any }
    });

    return order;
  } catch (error: any) {
    throw new Error('Failed to update order status');
  }
};
