import { Request, Response } from 'express';
import { getAllStoreOrders, getStoreOrderById, getStoreOrderStats, updateOrderStatus } from '../services/storeOrderService';
import { sendSuccess, sendError } from '@/utils/response';

export const getAllOrders = async (req: Request, res: Response): Promise<void> => {
  try {
    console.log('=== GET ALL ORDERS API CALLED ===');
    console.log('Query params:', req.query);

    const { status, search, startDate, endDate } = req.query;

    const orders = await getAllStoreOrders({
      status: status as string,
      search: search as string,
      startDate: startDate as string,
      endDate: endDate as string
    });

    console.log(`Successfully retrieved ${orders.length} orders`);
    sendSuccess(res, orders, 'Store orders retrieved successfully');
  } catch (error: any) {
    console.error('=== GET ALL ORDERS ERROR ===');
    console.error('Error details:', error);
    sendError(res, error.message || 'Failed to retrieve store orders', 500);
  }
};

export const getOrderDetails = async (req: Request, res: Response): Promise<void> => {
  try {
    const { orderId } = req.params;
    if (!orderId) {
      sendError(res, 'Order ID is required', 400);
      return;
    }

    const order = await getStoreOrderById(orderId);
    sendSuccess(res, order, 'Order details retrieved successfully');
  } catch (error: any) {
    sendError(res, error.message || 'Failed to retrieve order details', 500);
  }
};

export const getOrderStats = async (req: Request, res: Response): Promise<void> => {
  try {
    const stats = await getStoreOrderStats();
    sendSuccess(res, stats, 'Order statistics retrieved successfully');
  } catch (error: any) {
    sendError(res, error.message || 'Failed to retrieve order statistics', 500);
  }
};

export const updateOrder = async (req: Request, res: Response): Promise<void> => {
  try {
    const { orderId } = req.params;
    const { status } = req.body;

    if (!orderId) {
      sendError(res, 'Order ID is required', 400);
      return;
    }

    if (!status) {
      sendError(res, 'Status is required', 400);
      return;
    }

    const order = await updateOrderStatus(orderId, status);
    sendSuccess(res, order, 'Order status updated successfully');
  } catch (error: any) {
    sendError(res, error.message || 'Failed to update order status', 500);
  }
};

export const debugOrders = async (req: Request, res: Response): Promise<void> => {
  try {
    console.log('=== DEBUG ORDERS CONTROLLER CALLED ===');

    const prisma = require('@/config/prismaClient').default;

    // Get total count
    const totalCount = await prisma.storeOrder.count();
    console.log('Total orders count:', totalCount);

    // Get recent orders
    const recentOrders = await prisma.storeOrder.findMany({
      orderBy: { createdAt: 'desc' },
      take: 5
    });
    console.log('Recent orders:', recentOrders.length);

    // Get orders with item details (if possible)
    let ordersWithItems = [];
    try {
      ordersWithItems = await prisma.storeOrder.findMany({
        include: {
          item: true
        },
        orderBy: { createdAt: 'desc' },
        take: 3
      });
      console.log('Orders with items:', ordersWithItems.length);
    } catch (itemError: any) {
      console.log('Could not fetch with item relationship:', itemError.message);
    }

    // Get store items count
    const storeItemsCount = await prisma.storeItem.count();
    console.log('Store items count:', storeItemsCount);

    const debugData = {
      totalOrdersCount: totalCount,
      storeItemsCount: storeItemsCount,
      recentOrders: recentOrders,
      ordersWithItems: ordersWithItems,
      timestamp: new Date().toISOString()
    };

    console.log('Debug data prepared:', debugData);
    sendSuccess(res, debugData, 'Store orders debug information');

  } catch (error: any) {
    console.error('=== DEBUG ORDERS ERROR ===');
    console.error('Error details:', error);
    sendError(res, error.message || 'Failed to debug store orders', 500);
  }
};
