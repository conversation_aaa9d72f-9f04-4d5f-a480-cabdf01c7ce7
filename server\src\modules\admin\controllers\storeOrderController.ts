import { Request, Response } from 'express';
import { getStoreOrderById, getStoreOrderStats, updateOrderStatus } from '../services/storeOrderService';
import { sendSuccess, sendError } from '@/utils/response';

export const getAllOrders = async (req: Request, res: Response): Promise<void> => {
  try {
    const prisma = require('@/config/prismaClient').default;
    const { status, search, startDate, endDate } = req.query;

    console.log('=== GET ALL ORDERS API CALLED ===');
    console.log('Query params:', { status, search, startDate, endDate });

    let where: any = {};

    // Apply filters
    if (status) {
      where.status = status;
    }

    if (startDate || endDate) {
      where.createdAt = {};
      if (startDate) {
        where.createdAt.gte = new Date(startDate as string);
      }
      if (endDate) {
        where.createdAt.lte = new Date(endDate as string);
      }
    }

    if (search) {
      const searchTerm = (search as string).toLowerCase();
      where.OR = [
        { id: { contains: searchTerm, mode: 'insensitive' } },
        { studentId: { contains: searchTerm, mode: 'insensitive' } },
        { studentName: { contains: searchTerm, mode: 'insensitive' } },
        { studentEmail: { contains: searchTerm, mode: 'insensitive' } },
        { itemName: { contains: searchTerm, mode: 'insensitive' } }
      ];
    }

    // Get orders without item relationship first (safer approach)
    const orders = await prisma.storeOrder.findMany({
      where,
      orderBy: { createdAt: 'desc' }
    });

    console.log(`Successfully fetched ${orders.length} orders`);

    // Try to add item details manually for each order
    const ordersWithItems = await Promise.all(
      orders.map(async (order) => {
        try {
          const item = await prisma.storeItem.findUnique({
            where: { id: order.itemId }
          });
          return {
            ...order,
            item: item || null
          };
        } catch (itemError) {
          console.warn(`Could not fetch item for order ${order.id}:`, itemError);
          return {
            ...order,
            item: null
          };
        }
      })
    );

    console.log(`Added item details to ${ordersWithItems.filter(o => o.item).length} orders`);

    console.log(`Successfully retrieved ${ordersWithItems.length} orders`);
    sendSuccess(res, ordersWithItems, 'Store orders retrieved successfully');
  } catch (error: any) {
    console.error('Error retrieving store orders:', error);
    sendError(res, error.message || 'Failed to retrieve store orders', 500);
  }
};

export const getOrderDetails = async (req: Request, res: Response): Promise<void> => {
  try {
    const { orderId } = req.params;
    if (!orderId) {
      sendError(res, 'Order ID is required', 400);
      return;
    }

    const order = await getStoreOrderById(orderId);
    sendSuccess(res, order, 'Order details retrieved successfully');
  } catch (error: any) {
    sendError(res, error.message || 'Failed to retrieve order details', 500);
  }
};

export const getOrderStats = async (req: Request, res: Response): Promise<void> => {
  try {
    const stats = await getStoreOrderStats();
    sendSuccess(res, stats, 'Order statistics retrieved successfully');
  } catch (error: any) {
    sendError(res, error.message || 'Failed to retrieve order statistics', 500);
  }
};

export const updateOrder = async (req: Request, res: Response): Promise<void> => {
  try {
    const { orderId } = req.params;
    const { status } = req.body;

    if (!orderId) {
      sendError(res, 'Order ID is required', 400);
      return;
    }

    if (!status) {
      sendError(res, 'Status is required', 400);
      return;
    }

    const order = await updateOrderStatus(orderId, status);
    sendSuccess(res, order, 'Order status updated successfully');
  } catch (error: any) {
    sendError(res, error.message || 'Failed to update order status', 500);
  }
};




